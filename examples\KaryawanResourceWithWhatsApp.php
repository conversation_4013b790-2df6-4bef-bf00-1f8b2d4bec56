<?php

namespace App\Filament\Resources;

use App\Models\Karyawan;
use App\Helpers\WhatsAppHelper;
use App\Traits\HasWhatsAppNotifications;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
// ... other imports

class KaryawanResource extends Resource
{
    use HasWhatsAppNotifications;

    protected static ?string $model = Karyawan::class;
    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    // ... existing form method

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('nama_lengkap')
                    ->label('Nama Lengkap')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('no_hp')
                    ->label('No. HP')
                    ->searchable(),
                
                Tables\Columns\TextColumn::make('jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->sortable(),
                
                // ... other columns
            ])
            ->filters([
                // ... existing filters
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                
                // Tambahkan WhatsApp action menggunakan helper
                WhatsAppHelper::quickWhatsAppAction(
                    phoneField: 'no_hp',
                    nameField: 'nama_lengkap',
                    defaultMessage: 'Halo {name}, ini adalah pesan dari Tim HR. Terima kasih.'
                ),
                
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    
                    // Tambahkan bulk WhatsApp action
                    WhatsAppHelper::bulkWhatsAppAction(
                        phoneField: 'no_hp',
                        nameField: 'nama_lengkap'
                    ),
                ]),
            ]);
    }

    // ... rest of the resource
}

// Atau jika ingin menggunakan di ListKaryawan page:

namespace App\Filament\Resources\KaryawanResource\Pages;

use App\Filament\Resources\KaryawanResource;
use App\Traits\HasWhatsAppNotifications;
use App\Helpers\WhatsAppHelper;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListKaryawan extends ListRecords
{
    use HasWhatsAppNotifications;

    protected static string $resource = KaryawanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            
            // Method 1: Menggunakan trait
            $this->whatsAppNotificationAction(
                phoneField: 'no_hp',
                defaultMessage: 'Halo, ini adalah pesan dari sistem HR.'
            ),
            
            $this->bulkWhatsAppNotificationAction(
                phoneField: 'no_hp',
                nameField: 'nama_lengkap',
                defaultMessage: 'Halo {name}, ada pengumuman penting dari HR.'
            ),
            
            // Method 2: Menggunakan helper untuk template actions
            ...WhatsAppHelper::notificationTemplateActions(),
        ];
    }
}
