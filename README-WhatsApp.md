# 🚀 WhatsApp Integration - Quick Start Guide

Implementasi fitur WhatsApp notification menggunakan Starsender API untuk aplikasi Laravel Filament.

## ⚡ Quick Setup

### 1. Konfigurasi Environment

Tambahkan ke `.env`:
```env
STARSENDER_API_URL=https://api.starsender.online
STARSENDER_API_TOKEN=your_token_here
STARSENDER_ENABLED=true
```

### 2. Dapatkan Token Starsender

1. Daftar di [Starsender](https://app.starsender.online)
2. Buat device WhatsApp
3. Copy API token ke `.env`

### 3. Test Koneksi

```bash
php artisan whatsapp:test 6281234567890 "Test message"
```

## 🎯 Cara Penggunaan

### Method 1: Menggunakan Helper (Paling Mudah)

```php
use App\Helpers\WhatsAppHelper;

// Di table actions
->actions([
    WhatsAppHelper::quickWhatsAppAction(
        phoneField: 'no_hp',
        nameField: 'nama_lengkap',
        defaultMessage: 'Hal<PERSON> {name}, pesan dari HR.'
    ),
])

// Di bulk actions
->bulkActions([
    WhatsAppHelper::bulkWhatsAppAction(
        phoneField: 'no_hp',
        nameField: 'nama_lengkap'
    ),
])
```

### Method 2: Menggunakan Trait

```php
use App\Traits\HasWhatsAppNotifications;

class ListKaryawan extends ListRecords
{
    use HasWhatsAppNotifications;

    protected function getHeaderActions(): array
    {
        return [
            $this->whatsAppNotificationAction(
                phoneField: 'no_hp',
                defaultMessage: 'Halo, pesan dari sistem.'
            ),
            
            $this->bulkWhatsAppNotificationAction(
                phoneField: 'no_hp',
                nameField: 'nama_lengkap',
                defaultMessage: 'Halo {name}, pengumuman penting.'
            ),
        ];
    }
}
```

### Method 3: Menggunakan Service Langsung

```php
use App\Services\WhatsAppService;

$whatsAppService = app(WhatsAppService::class);

// Kirim pesan tunggal
$result = $whatsAppService->sendMessage('6281234567890', 'Halo!');

// Kirim pesan massal
$recipients = [
    ['phone' => '6281234567890', 'message' => 'Pesan 1'],
    ['phone' => '6281234567891', 'message' => 'Pesan 2'],
];
$results = $whatsAppService->sendBulkMessages($recipients);
```

## 📋 Template Actions

Gunakan template actions yang sudah siap pakai:

```php
use App\Helpers\WhatsAppHelper;

// Di header actions
protected function getHeaderActions(): array
{
    return [
        ...WhatsAppHelper::notificationTemplateActions(),
        // Akan menambahkan: Pengingat, Konfirmasi, Pengumuman
    ];
}
```

## 🔧 Custom Actions untuk Approval

Untuk fitur approval dengan auto-WhatsApp:

```php
use App\Filament\Resources\CutiIzinResource\Actions\WhatsAppCutiActions;

->actions([
    WhatsAppCutiActions::approveWithWhatsApp(),
    WhatsAppCutiActions::rejectWithWhatsApp(),
    WhatsAppCutiActions::manualWhatsAppAction(),
])
```

## 🤖 Auto-Notification dengan Observer

```php
// Di Observer
public function updated(CutiIzin $cutiIzin)
{
    if ($cutiIzin->isDirty('status')) {
        $whatsAppService = app(WhatsAppService::class);
        $whatsAppService->sendMessage(
            $cutiIzin->karyawan->no_hp,
            "Status cuti Anda: {$cutiIzin->status}"
        );
    }
}
```

## 📱 Format Nomor Telepon

Service otomatis memformat nomor:
- `081234567890` → `6281234567890`
- `+6281234567890` → `6281234567890`
- `6281234567890` → `6281234567890` (tidak berubah)

## ✅ Best Practices

1. **Selalu cek status service** sebelum kirim pesan
2. **Handle error gracefully** - jangan biarkan error WhatsApp mengganggu proses utama
3. **Gunakan placeholder** seperti `{name}` untuk personalisasi
4. **Test dengan nomor sendiri** sebelum production

## 🔍 Troubleshooting

### WhatsApp tidak terkirim
```bash
# Cek status service
php artisan tinker
>>> app(\App\Services\WhatsAppService::class)->getStatus()
```

### Clear cache setelah update .env
```bash
php artisan config:clear
```

### Cek log error
```bash
tail -f storage/logs/laravel.log
```

## 📁 File Structure

```
app/
├── Services/WhatsAppService.php          # Core service
├── Traits/HasWhatsAppNotifications.php   # Trait untuk Filament
├── Helpers/WhatsAppHelper.php            # Helper functions
├── Console/Commands/TestWhatsAppCommand.php # Test command
└── Filament/Resources/.../Actions/WhatsAppCutiActions.php # Custom actions

config/services.php                       # Konfigurasi
docs/whatsapp-integration.md             # Dokumentasi lengkap
examples/                                 # Contoh implementasi
```

## 🎉 Selesai!

Sekarang Anda bisa mengirim WhatsApp notification dari action Filament manapun dengan mudah!

Untuk dokumentasi lengkap, lihat: `docs/whatsapp-integration.md`
