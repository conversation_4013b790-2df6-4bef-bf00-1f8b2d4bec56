<div class="space-y-6">
    <!-- Header <PERSON> -->
    <div class="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div class="flex-shrink-0">
            <!--[if BLOCK]><![endif]--><?php if($karyawan->foto_profil): ?>
                <img src="<?php echo e(Storage::url($karyawan->foto_profil)); ?>" 
                     alt="<?php echo e($karyawan->nama_lengkap); ?>" 
                     class="w-16 h-16 rounded-full object-cover">
            <?php else: ?>
                <div class="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
        <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo e($karyawan->nama_lengkap); ?></h3>
            <p class="text-sm text-gray-600 dark:text-gray-400"><?php echo e($karyawan->nip ?? 'NIP tidak tersedia'); ?></p>
            <p class="text-sm text-gray-600 dark:text-gray-400">
                <?php echo e($karyawan->jabatan->nama_jabatan ?? 'Jabatan tidak tersedia'); ?>

                <!--[if BLOCK]><![endif]--><?php if($karyawan->divisi): ?>
                    - <?php echo e($karyawan->divisi->nama_divisi); ?>

                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                <!--[if BLOCK]><![endif]--><?php if($karyawan->departemen): ?>
                    - <?php echo e($karyawan->departemen->nama_departemen); ?>

                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </p>
            <!--[if BLOCK]><![endif]--><?php if($karyawan->entitas): ?>
                <p class="text-sm text-gray-600 dark:text-gray-400"><?php echo e($karyawan->entitas->nama); ?></p>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>

    <!-- Status Hari Ini -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Status Utama -->
        <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Status Hari Ini</h4>
            <div class="flex items-center space-x-2">
                <?php
                    $status = $statusInfo['status'];
                    $statusConfig = [
                        'hadir' => ['color' => 'green', 'icon' => '✅', 'label' => 'Hadir'],
                        'terlambat' => ['color' => 'yellow', 'icon' => '⏰', 'label' => 'Terlambat'],
                        'alpha' => ['color' => 'red', 'icon' => '❌', 'label' => 'Alpha'],
                        'sakit' => ['color' => 'red', 'icon' => '🤒', 'label' => 'Sakit'],
                        'cuti' => ['color' => 'blue', 'icon' => '🏖️', 'label' => 'Cuti'],
                        'izin' => ['color' => 'blue', 'icon' => '📝', 'label' => 'Izin'],
                        'libur' => ['color' => 'gray', 'icon' => '🌙', 'label' => 'Libur'],
                        'tidak_ada_jadwal' => ['color' => 'gray', 'icon' => '➖', 'label' => 'Tidak Ada Jadwal'],
                    ];
                    $config = $statusConfig[$status] ?? ['color' => 'gray', 'icon' => '❓', 'label' => 'Tidak Diketahui'];
                ?>
                
                <span class="text-2xl"><?php echo e($config['icon']); ?></span>
                <span class="px-3 py-1 rounded-full text-sm font-medium
                    <?php if($config['color'] === 'green'): ?> bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                    <?php elseif($config['color'] === 'yellow'): ?> bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                    <?php elseif($config['color'] === 'red'): ?> bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                    <?php elseif($config['color'] === 'blue'): ?> bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                    <?php else: ?> bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                    <?php endif; ?>">
                    <?php echo e($config['label']); ?>

                </span>
            </div>
            <!--[if BLOCK]><![endif]--><?php if($statusInfo['keterangan']): ?>
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400"><?php echo e($statusInfo['keterangan']); ?></p>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        <!-- Kontak -->
        <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Kontak</h4>
            <!--[if BLOCK]><![endif]--><?php if($karyawan->no_hp): ?>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-600 dark:text-gray-400">HP:</span>
                    <a href="tel:<?php echo e($karyawan->no_hp); ?>" 
                       class="text-blue-600 dark:text-blue-400 hover:underline">
                        <?php echo e($karyawan->no_hp); ?>

                    </a>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            <!--[if BLOCK]><![endif]--><?php if($karyawan->email): ?>
                <div class="flex items-center space-x-2 mt-1">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Email:</span>
                    <a href="mailto:<?php echo e($karyawan->email); ?>" 
                       class="text-blue-600 dark:text-blue-400 hover:underline">
                        <?php echo e($karyawan->email); ?>

                    </a>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>

    <!-- Detail Informasi -->
    <div class="space-y-4">
        <!-- Jadwal Hari Ini -->
        <!--[if BLOCK]><![endif]--><?php if($statusInfo['jadwal']): ?>
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Jadwal Hari Ini</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Shift:</span>
                        <span class="ml-2 font-medium"><?php echo e($statusInfo['jadwal']->shift->nama_shift ?? 'Tidak tersedia'); ?></span>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Waktu:</span>
                        <span class="ml-2 font-medium">
                            <?php echo e($statusInfo['jadwal']->waktu_masuk ? \Carbon\Carbon::parse($statusInfo['jadwal']->waktu_masuk)->format('H:i') : '-'); ?> - 
                            <?php echo e($statusInfo['jadwal']->waktu_keluar ? \Carbon\Carbon::parse($statusInfo['jadwal']->waktu_keluar)->format('H:i') : '-'); ?>

                        </span>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Jadwal Hari Ini</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">Tidak ada jadwal kerja hari ini</p>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Absensi Hari Ini -->
        <!--[if BLOCK]><![endif]--><?php if($statusInfo['absensi']): ?>
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Absensi Hari Ini</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Waktu Masuk:</span>
                        <span class="ml-2 font-medium">
                            <?php echo e($statusInfo['absensi']->waktu_masuk ? \Carbon\Carbon::parse($statusInfo['absensi']->waktu_masuk)->format('H:i:s') : 'Belum absen'); ?>

                        </span>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Waktu Keluar:</span>
                        <span class="ml-2 font-medium">
                            <?php echo e($statusInfo['absensi']->waktu_keluar ? \Carbon\Carbon::parse($statusInfo['absensi']->waktu_keluar)->format('H:i:s') : 'Belum absen'); ?>

                        </span>
                    </div>
                </div>
                <!--[if BLOCK]><![endif]--><?php if($statusInfo['absensi']->keterangan): ?>
                    <div class="mt-2">
                        <span class="text-gray-600 dark:text-gray-400">Keterangan:</span>
                        <span class="ml-2"><?php echo e($statusInfo['absensi']->keterangan); ?></span>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Cuti/Izin/Sakit -->
        <!--[if BLOCK]><![endif]--><?php if($statusInfo['cutiIzin']): ?>
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h4 class="font-semibold text-gray-900 dark:text-white mb-2">
                    <?php echo e(ucfirst($statusInfo['cutiIzin']->jenis_permohonan)); ?> yang Sedang Berlangsung
                </h4>
                <div class="space-y-2 text-sm">
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Periode:</span>
                        <span class="ml-2 font-medium">
                            <?php echo e($statusInfo['cutiIzin']->tanggal_mulai->format('d M Y')); ?> - 
                            <?php echo e($statusInfo['cutiIzin']->tanggal_selesai->format('d M Y')); ?>

                        </span>
                        <span class="ml-2 text-gray-500">(<?php echo e($statusInfo['cutiIzin']->jumlah_hari); ?> hari)</span>
                    </div>
                    <div>
                        <span class="text-gray-600 dark:text-gray-400">Alasan:</span>
                        <span class="ml-2"><?php echo e($statusInfo['cutiIzin']->alasan); ?></span>
                    </div>
                    <!--[if BLOCK]><![endif]--><?php if($statusInfo['cutiIzin']->keterangan_tambahan): ?>
                        <div>
                            <span class="text-gray-600 dark:text-gray-400">Keterangan Tambahan:</span>
                            <span class="ml-2"><?php echo e($statusInfo['cutiIzin']->keterangan_tambahan); ?></span>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]--><?php if($statusInfo['cutiIzin']->approved_by): ?>
                        <div>
                            <span class="text-gray-600 dark:text-gray-400">Disetujui oleh:</span>
                            <span class="ml-2"><?php echo e($statusInfo['cutiIzin']->approvedBy->name ?? 'Admin'); ?></span>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!-- Footer Info -->
    <div class="text-xs text-gray-500 dark:text-gray-400 text-center pt-4 border-t border-gray-200 dark:border-gray-700">
        Data diperbarui secara real-time • <?php echo e(\Carbon\Carbon::now()->format('d M Y H:i:s')); ?>

    </div>
</div>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/admin/modals/status-detail.blade.php ENDPATH**/ ?>