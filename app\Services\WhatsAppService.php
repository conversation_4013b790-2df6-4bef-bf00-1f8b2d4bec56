<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class WhatsAppService
{
    protected string $apiUrl;
    protected string $apiToken;
    protected bool $enabled;

    public function __construct()
    {
        $this->apiUrl = config('services.starsender.api_url');
        $this->apiToken = config('services.starsender.api_token');
        $this->enabled = config('services.starsender.enabled', true);
    }

    /**
     * Send a WhatsApp message
     *
     * @param string $phoneNumber Phone number with country code (e.g., 6281234567890)
     * @param string $message Message content
     * @param array $options Additional options (media, etc.)
     * @return array Response from API
     * @throws Exception
     */
    public function sendMessage(string $phoneNumber, string $message, array $options = []): array
    {
        if (!$this->enabled) {
            Log::info('WhatsApp service is disabled');
            return ['success' => false, 'message' => 'WhatsApp service is disabled'];
        }

        if (empty($this->apiToken)) {
            throw new Exception('Starsender API token is not configured');
        }

        // Clean phone number (remove spaces, dashes, etc.)
        $phoneNumber = $this->cleanPhoneNumber($phoneNumber);

        // Validate phone number
        if (!$this->isValidPhoneNumber($phoneNumber)) {
            throw new Exception('Invalid phone number format');
        }

        $payload = [
            'number' => $phoneNumber,
            'message' => $message,
        ];

        // Add media if provided
        if (isset($options['media_url'])) {
            $payload['media'] = $options['media_url'];
        }

        // Add caption for media
        if (isset($options['caption'])) {
            $payload['caption'] = $options['caption'];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiToken,
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl . '/api/send-message', $payload);

            $responseData = $response->json();

            if ($response->successful()) {
                Log::info('WhatsApp message sent successfully', [
                    'phone' => $phoneNumber,
                    'message_length' => strlen($message),
                    'response' => $responseData
                ]);

                return [
                    'success' => true,
                    'data' => $responseData,
                    'message' => 'Message sent successfully'
                ];
            } else {
                Log::error('Failed to send WhatsApp message', [
                    'phone' => $phoneNumber,
                    'status' => $response->status(),
                    'response' => $responseData
                ]);

                return [
                    'success' => false,
                    'error' => $responseData['message'] ?? 'Unknown error',
                    'status_code' => $response->status()
                ];
            }
        } catch (Exception $e) {
            Log::error('WhatsApp service error', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);

            throw new Exception('Failed to send WhatsApp message: ' . $e->getMessage());
        }
    }

    /**
     * Send a WhatsApp message with media
     *
     * @param string $phoneNumber
     * @param string $mediaUrl
     * @param string $caption
     * @return array
     * @throws Exception
     */
    public function sendMediaMessage(string $phoneNumber, string $mediaUrl, string $caption = ''): array
    {
        return $this->sendMessage($phoneNumber, $caption, [
            'media_url' => $mediaUrl,
            'caption' => $caption
        ]);
    }

    /**
     * Send bulk WhatsApp messages
     *
     * @param array $recipients Array of ['phone' => '...', 'message' => '...']
     * @return array Results for each recipient
     */
    public function sendBulkMessages(array $recipients): array
    {
        $results = [];

        foreach ($recipients as $recipient) {
            try {
                $result = $this->sendMessage(
                    $recipient['phone'],
                    $recipient['message'],
                    $recipient['options'] ?? []
                );
                $results[] = array_merge($result, ['phone' => $recipient['phone']]);
            } catch (Exception $e) {
                $results[] = [
                    'success' => false,
                    'phone' => $recipient['phone'],
                    'error' => $e->getMessage()
                ];
            }

            // Add small delay between messages to avoid rate limiting
            usleep(500000); // 0.5 seconds
        }

        return $results;
    }

    /**
     * Clean phone number format
     *
     * @param string $phoneNumber
     * @return string
     */
    protected function cleanPhoneNumber(string $phoneNumber): string
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);

        // Add country code if not present (assuming Indonesia +62)
        if (substr($cleaned, 0, 2) !== '62') {
            if (substr($cleaned, 0, 1) === '0') {
                $cleaned = '62' . substr($cleaned, 1);
            } else {
                $cleaned = '62' . $cleaned;
            }
        }

        return $cleaned;
    }

    /**
     * Validate phone number format
     *
     * @param string $phoneNumber
     * @return bool
     */
    protected function isValidPhoneNumber(string $phoneNumber): bool
    {
        // Basic validation for Indonesian phone numbers
        return preg_match('/^62[0-9]{8,13}$/', $phoneNumber);
    }

    /**
     * Check if service is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->enabled && !empty($this->apiToken);
    }

    /**
     * Get service status
     *
     * @return array
     */
    public function getStatus(): array
    {
        return [
            'enabled' => $this->enabled,
            'configured' => !empty($this->apiToken),
            'api_url' => $this->apiUrl,
        ];
    }
}
