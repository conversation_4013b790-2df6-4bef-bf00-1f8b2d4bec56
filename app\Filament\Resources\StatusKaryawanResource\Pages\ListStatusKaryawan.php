<?php

namespace App\Filament\Resources\StatusKaryawanResource\Pages;

use App\Filament\Resources\StatusKaryawanResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Karyawan;
use App\Models\CutiIzin;
use App\Models\Schedule;
use App\Models\Absensi;
use Carbon\Carbon;

class ListStatusKaryawan extends ListRecords
{
    protected static string $resource = StatusKaryawanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('refresh')
                ->label('Refresh Data')
                ->icon('heroicon-o-arrow-path')
                ->action(function () {
                    $this->redirect(request()->header('Referer'));
                }),
        ];
    }

    public function getTabs(): array
    {
        $today = Carbon::today();

        return [
            'semua' => Tab::make('Semu<PERSON> Ka<PERSON>wan')
                ->badge($this->getTotalKaryawan()),

            'cuti_izin_sakit' => Tab::make('Cuti/Izin/Sakit')
                ->modifyQueryUsing(function (Builder $query) use ($today) {
                    $cutiIds = CutiIzin::where('status', 'approved')
                        ->whereDate('tanggal_mulai', '<=', $today)
                        ->whereDate('tanggal_selesai', '>=', $today)
                        ->pluck('karyawan_id');
                    
                    return $query->whereIn('id', $cutiIds);
                })
                ->badge($this->getKaryawanCutiIzinSakit()),

            'libur' => Tab::make('Libur/Tidak Ada Jadwal')
                ->modifyQueryUsing(function (Builder $query) use ($today) {
                    $scheduledIds = Schedule::whereDate('tanggal_jadwal', $today)
                        ->pluck('karyawan_id');
                    
                    return $query->whereNotIn('id', $scheduledIds);
                })
                ->badge($this->getKaryawanLibur()),

            'hadir' => Tab::make('Hadir')
                ->modifyQueryUsing(function (Builder $query) use ($today) {
                    $hadirIds = Absensi::whereDate('tanggal_absensi', $today)
                        ->where('status', 'hadir')
                        ->pluck('karyawan_id');
                    
                    return $query->whereIn('id', $hadirIds);
                })
                ->badge($this->getKaryawanHadir()),

            'terlambat' => Tab::make('Terlambat')
                ->modifyQueryUsing(function (Builder $query) use ($today) {
                    $terlambatIds = Absensi::whereDate('tanggal_absensi', $today)
                        ->where('status', 'terlambat')
                        ->pluck('karyawan_id');
                    
                    return $query->whereIn('id', $terlambatIds);
                })
                ->badge($this->getKaryawanTerlambat()),

            'alpha' => Tab::make('Alpha')
                ->modifyQueryUsing(function (Builder $query) use ($today) {
                    // Karyawan yang ada jadwal tapi tidak absen
                    $scheduledIds = Schedule::whereDate('tanggal_jadwal', $today)
                        ->pluck('karyawan_id');
                    
                    $absenIds = Absensi::whereDate('tanggal_absensi', $today)
                        ->pluck('karyawan_id');
                    
                    $cutiIds = CutiIzin::where('status', 'approved')
                        ->whereDate('tanggal_mulai', '<=', $today)
                        ->whereDate('tanggal_selesai', '>=', $today)
                        ->pluck('karyawan_id');
                    
                    $alphaIds = $scheduledIds->diff($absenIds)->diff($cutiIds);
                    
                    return $query->whereIn('id', $alphaIds);
                })
                ->badge($this->getKaryawanAlpha()),
        ];
    }

    protected function getTotalKaryawan(): int
    {
        return Karyawan::where('status_aktif', true)->count();
    }

    protected function getKaryawanCutiIzinSakit(): int
    {
        $today = Carbon::today();
        
        return CutiIzin::where('status', 'approved')
            ->whereDate('tanggal_mulai', '<=', $today)
            ->whereDate('tanggal_selesai', '>=', $today)
            ->distinct('karyawan_id')
            ->count();
    }

    protected function getKaryawanLibur(): int
    {
        $today = Carbon::today();
        
        $scheduledIds = Schedule::whereDate('tanggal_jadwal', $today)
            ->pluck('karyawan_id');
        
        return Karyawan::where('status_aktif', true)
            ->whereNotIn('id', $scheduledIds)
            ->count();
    }

    protected function getKaryawanHadir(): int
    {
        $today = Carbon::today();
        
        return Absensi::whereDate('tanggal_absensi', $today)
            ->where('status', 'hadir')
            ->distinct('karyawan_id')
            ->count();
    }

    protected function getKaryawanTerlambat(): int
    {
        $today = Carbon::today();
        
        return Absensi::whereDate('tanggal_absensi', $today)
            ->where('status', 'terlambat')
            ->distinct('karyawan_id')
            ->count();
    }

    protected function getKaryawanAlpha(): int
    {
        $today = Carbon::today();
        
        // Karyawan yang ada jadwal tapi tidak absen dan tidak cuti
        $scheduledIds = Schedule::whereDate('tanggal_jadwal', $today)
            ->pluck('karyawan_id');
        
        $absenIds = Absensi::whereDate('tanggal_absensi', $today)
            ->pluck('karyawan_id');
        
        $cutiIds = CutiIzin::where('status', 'approved')
            ->whereDate('tanggal_mulai', '<=', $today)
            ->whereDate('tanggal_selesai', '>=', $today)
            ->pluck('karyawan_id');
        
        return $scheduledIds->diff($absenIds)->diff($cutiIds)->count();
    }

    public function getTitle(): string
    {
        return 'Status Karyawan - ' . Carbon::today()->format('d F Y');
    }

    public function getHeading(): string
    {
        return 'Status Karyawan Hari Ini';
    }

    public function getSubheading(): string
    {
        return 'Monitoring status kehadiran karyawan pada ' . Carbon::today()->format('l, d F Y');
    }
}
