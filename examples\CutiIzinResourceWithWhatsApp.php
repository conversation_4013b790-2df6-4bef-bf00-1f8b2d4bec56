<?php

namespace App\Filament\Resources;

use App\Models\CutiIzin;
use App\Helpers\WhatsAppHelper;
use App\Filament\Resources\CutiIzinResource\Actions\WhatsAppCutiActions;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Resources\Resource;
// ... other imports

class CutiIzinResource extends Resource
{
    protected static ?string $model = CutiIzin::class;

    // ... existing form method

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('karyawan.nama_lengkap')
                    ->label('Karyawan')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('jenis_permohonan_label')
                    ->label('Jenis')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Cuti' => 'success',
                        'Izin' => 'warning',
                        'Sakit' => 'danger',
                        default => 'gray',
                    }),
                
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'approved' => 'success',
                        'rejected' => 'danger',
                        'pending' => 'warning',
                        default => 'gray',
                    }),
                
                // ... other columns
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                
                // Gunakan custom actions yang sudah include WhatsApp
                WhatsAppCutiActions::approveWithWhatsApp(),
                WhatsAppCutiActions::rejectWithWhatsApp(),
                WhatsAppCutiActions::manualWhatsAppAction(),
                
                // Atau gunakan helper untuk quick WhatsApp
                WhatsAppHelper::quickWhatsAppAction(
                    phoneField: 'karyawan.no_hp',
                    nameField: 'karyawan.nama_lengkap',
                    defaultMessage: 'Halo {name}, terkait permohonan cuti/izin Anda...'
                ),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    
                    // Bulk action untuk notifikasi WhatsApp
                    WhatsAppHelper::bulkWhatsAppAction(
                        phoneField: 'karyawan.no_hp',
                        nameField: 'karyawan.nama_lengkap'
                    ),
                ]),
            ]);
    }

    // ... rest of the resource
}

// Contoh penggunaan di Observer untuk auto-notification

namespace App\Observers;

use App\Models\CutiIzin;
use App\Services\WhatsAppService;
use Illuminate\Support\Facades\Log;

class CutiIzinObserver
{
    public function updated(CutiIzin $cutiIzin)
    {
        // Auto-send WhatsApp when status changes
        if ($cutiIzin->isDirty('status') && in_array($cutiIzin->status, ['approved', 'rejected'])) {
            $this->sendStatusNotification($cutiIzin);
        }
    }

    protected function sendStatusNotification(CutiIzin $cutiIzin)
    {
        try {
            $whatsAppService = app(WhatsAppService::class);
            
            if (!$whatsAppService->isEnabled()) {
                return;
            }

            $karyawan = $cutiIzin->karyawan;
            $phoneNumber = $karyawan->no_hp ?? null;

            if (empty($phoneNumber)) {
                return;
            }

            $message = $this->createStatusMessage($cutiIzin);
            $whatsAppService->sendMessage($phoneNumber, $message);

        } catch (\Exception $e) {
            Log::error('Auto WhatsApp notification failed', [
                'cuti_izin_id' => $cutiIzin->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    protected function createStatusMessage(CutiIzin $cutiIzin): string
    {
        $karyawan = $cutiIzin->karyawan;
        $status = $cutiIzin->status === 'approved' ? 'DISETUJUI' : 'DITOLAK';
        $jenis = $cutiIzin->jenis_permohonan_label;
        
        $message = "Halo {$karyawan->nama_lengkap},\n\n";
        $message .= "Permohonan {$jenis} Anda telah *{$status}*.\n\n";
        
        if ($cutiIzin->status === 'rejected' && $cutiIzin->rejection_reason) {
            $message .= "Alasan: {$cutiIzin->rejection_reason}\n\n";
        }
        
        $message .= "Silakan cek sistem untuk detail lengkap.\n\n";
        $message .= "Terima kasih,\nTim HRD";

        return $message;
    }
}
