<?php

namespace App\Filament\Karyawan\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Karyawan;
use App\Models\CutiIzin;
use App\Models\Schedule;
use App\Models\Absensi;
use Carbon\Carbon;

class StatusKaryawanOverview extends BaseWidget
{
    protected static ?int $sort = 1;
    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        $today = Carbon::today();

        return [
            Stat::make('Total Karyawan Aktif', $this->getTotalKaryawan())
                ->description('Karyawan dengan status aktif')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary')
                ->url(route('filament.karyawan.resources.status-karyawans.index')),

            Stat::make('Hadir Hari Ini', $this->getKaryawanHadir())
                ->description('Karyawan yang sudah absen hadir')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success')
                ->url(route('filament.karyawan.resources.status-karyawans.index', ['activeTab' => 'hadir'])),

            Stat::make('Cuti/Izin/Sakit', $this->getKaryawanCutiIzinSakit())
                ->description('Karyawan yang sedang cuti/izin/sakit')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('info')
                ->url(route('filament.karyawan.resources.status-karyawans.index', ['activeTab' => 'cuti_izin_sakit'])),

            Stat::make('Terlambat', $this->getKaryawanTerlambat())
                ->description('Karyawan yang terlambat hari ini')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning')
                ->url(route('filament.karyawan.resources.status-karyawans.index', ['activeTab' => 'terlambat'])),

            Stat::make('Alpha', $this->getKaryawanAlpha())
                ->description('Karyawan yang tidak hadir tanpa keterangan')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('danger')
                ->url(route('filament.karyawan.resources.status-karyawans.index', ['activeTab' => 'alpha'])),

            Stat::make('Libur/Tidak Ada Jadwal', $this->getKaryawanLibur())
                ->description('Karyawan yang tidak memiliki jadwal hari ini')
                ->descriptionIcon('heroicon-m-moon')
                ->color('gray')
                ->url(route('filament.karyawan.resources.status-karyawans.index', ['activeTab' => 'libur'])),
        ];
    }

    protected function getTotalKaryawan(): int
    {
        return Karyawan::where('status_aktif', true)->count();
    }

    protected function getKaryawanHadir(): int
    {
        $today = Carbon::today();
        
        return Absensi::whereDate('tanggal_absensi', $today)
            ->where('status', 'hadir')
            ->distinct('karyawan_id')
            ->count();
    }

    protected function getKaryawanCutiIzinSakit(): int
    {
        $today = Carbon::today();
        
        return CutiIzin::where('status', 'approved')
            ->whereDate('tanggal_mulai', '<=', $today)
            ->whereDate('tanggal_selesai', '>=', $today)
            ->distinct('karyawan_id')
            ->count();
    }

    protected function getKaryawanTerlambat(): int
    {
        $today = Carbon::today();
        
        return Absensi::whereDate('tanggal_absensi', $today)
            ->where('status', 'terlambat')
            ->distinct('karyawan_id')
            ->count();
    }

    protected function getKaryawanAlpha(): int
    {
        $today = Carbon::today();
        
        // Karyawan yang ada jadwal tapi tidak absen dan tidak cuti
        $scheduledIds = Schedule::whereDate('tanggal_jadwal', $today)
            ->pluck('karyawan_id');
        
        $absenIds = Absensi::whereDate('tanggal_absensi', $today)
            ->pluck('karyawan_id');
        
        $cutiIds = CutiIzin::where('status', 'approved')
            ->whereDate('tanggal_mulai', '<=', $today)
            ->whereDate('tanggal_selesai', '>=', $today)
            ->pluck('karyawan_id');
        
        return $scheduledIds->diff($absenIds)->diff($cutiIds)->count();
    }

    protected function getKaryawanLibur(): int
    {
        $today = Carbon::today();
        
        $scheduledIds = Schedule::whereDate('tanggal_jadwal', $today)
            ->pluck('karyawan_id');
        
        return Karyawan::where('status_aktif', true)
            ->whereNotIn('id', $scheduledIds)
            ->count();
    }

    public function getHeading(): string
    {
        return 'Status Karyawan - ' . Carbon::today()->format('d F Y');
    }
}
