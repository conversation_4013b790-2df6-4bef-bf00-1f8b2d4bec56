<?php

namespace App\Helpers;

use App\Services\WhatsAppService;
use Filament\Tables\Actions\Action;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Exception;

class WhatsAppHelper
{
    /**
     * Create a quick WhatsApp action for any record
     *
     * @param string $phoneField Field name containing phone number
     * @param string $nameField Field name containing recipient name
     * @param string $defaultMessage Default message template
     * @return Action
     */
    public static function quickWhatsAppAction(
        string $phoneField = 'phone',
        string $nameField = 'name',
        string $defaultMessage = ''
    ): Action {
        return Action::make('quickWhatsApp')
            ->label('WhatsApp')
            ->icon('heroicon-o-chat-bubble-left-right')
            ->color('success')
            ->size('sm')
            ->form([
                TextInput::make('phone_number')
                    ->label('Nomor WhatsApp')
                    ->required()
                    ->default(function ($record) use ($phoneField) {
                        return $record->{$phoneField} ?? '';
                    }),
                
                Textarea::make('message')
                    ->label('Pesan')
                    ->required()
                    ->rows(3)
                    ->default(function ($record) use ($nameField, $defaultMessage) {
                        $name = $record->{$nameField} ?? 'Pengguna';
                        return str_replace('{name}', $name, $defaultMessage);
                    }),
            ])
            ->action(function ($record, array $data) use ($nameField) {
                self::sendQuickMessage(
                    $data['phone_number'],
                    $data['message'],
                    $record->{$nameField} ?? 'Pengguna'
                );
            })
            ->visible(function ($record) use ($phoneField) {
                $whatsAppService = app(WhatsAppService::class);
                return $whatsAppService->isEnabled() && !empty($record->{$phoneField});
            });
    }

    /**
     * Create notification template actions for common scenarios
     */
    public static function notificationTemplateActions(): array
    {
        return [
            self::reminderAction(),
            self::confirmationAction(),
            self::announcementAction(),
        ];
    }

    /**
     * Reminder action template
     */
    public static function reminderAction(): Action
    {
        return Action::make('sendReminder')
            ->label('Kirim Pengingat')
            ->icon('heroicon-o-bell')
            ->color('warning')
            ->form([
                TextInput::make('phone_number')
                    ->label('Nomor WhatsApp')
                    ->required(),
                
                Textarea::make('reminder_message')
                    ->label('Pesan Pengingat')
                    ->required()
                    ->default('Halo, ini adalah pengingat penting untuk Anda. Terima kasih.')
                    ->rows(3),
            ])
            ->action(function (array $data) {
                self::sendQuickMessage(
                    $data['phone_number'],
                    $data['reminder_message'],
                    'Penerima'
                );
            });
    }

    /**
     * Confirmation action template
     */
    public static function confirmationAction(): Action
    {
        return Action::make('sendConfirmation')
            ->label('Kirim Konfirmasi')
            ->icon('heroicon-o-check-circle')
            ->color('success')
            ->form([
                TextInput::make('phone_number')
                    ->label('Nomor WhatsApp')
                    ->required(),
                
                Textarea::make('confirmation_message')
                    ->label('Pesan Konfirmasi')
                    ->required()
                    ->default('Halo, kami konfirmasi bahwa data/permintaan Anda telah diproses. Terima kasih.')
                    ->rows(3),
            ])
            ->action(function (array $data) {
                self::sendQuickMessage(
                    $data['phone_number'],
                    $data['confirmation_message'],
                    'Penerima'
                );
            });
    }

    /**
     * Announcement action template
     */
    public static function announcementAction(): Action
    {
        return Action::make('sendAnnouncement')
            ->label('Kirim Pengumuman')
            ->icon('heroicon-o-megaphone')
            ->color('info')
            ->form([
                TextInput::make('phone_number')
                    ->label('Nomor WhatsApp')
                    ->required(),
                
                Textarea::make('announcement_message')
                    ->label('Pesan Pengumuman')
                    ->required()
                    ->default('Halo, ada pengumuman penting untuk Anda. Silakan cek sistem untuk detail lengkap.')
                    ->rows(4),
            ])
            ->action(function (array $data) {
                self::sendQuickMessage(
                    $data['phone_number'],
                    $data['announcement_message'],
                    'Penerima'
                );
            });
    }

    /**
     * Send quick WhatsApp message
     */
    protected static function sendQuickMessage(string $phoneNumber, string $message, string $recipientName): void
    {
        try {
            $whatsAppService = app(WhatsAppService::class);
            
            if (!$whatsAppService->isEnabled()) {
                Notification::make()
                    ->title('WhatsApp Tidak Aktif')
                    ->body('Layanan WhatsApp sedang tidak aktif')
                    ->warning()
                    ->send();
                return;
            }

            $result = $whatsAppService->sendMessage($phoneNumber, $message);

            if ($result['success']) {
                Notification::make()
                    ->title('WhatsApp Terkirim')
                    ->body("Pesan berhasil dikirim ke {$recipientName}")
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Gagal Mengirim WhatsApp')
                    ->body($result['error'] ?? 'Terjadi kesalahan saat mengirim pesan')
                    ->danger()
                    ->send();
            }
        } catch (Exception $e) {
            Notification::make()
                ->title('Error WhatsApp')
                ->body('Terjadi kesalahan: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Get WhatsApp status info for debugging
     */
    public static function getStatusInfo(): array
    {
        $whatsAppService = app(WhatsAppService::class);
        return $whatsAppService->getStatus();
    }

    /**
     * Create bulk WhatsApp action for selected records
     */
    public static function bulkWhatsAppAction(
        string $phoneField = 'phone',
        string $nameField = 'name'
    ): \Filament\Tables\Actions\BulkAction {
        return \Filament\Tables\Actions\BulkAction::make('bulkWhatsApp')
            ->label('Kirim WhatsApp Massal')
            ->icon('heroicon-o-chat-bubble-left-right')
            ->color('warning')
            ->form([
                Textarea::make('bulk_message')
                    ->label('Pesan untuk Semua')
                    ->required()
                    ->placeholder('Tulis pesan yang akan dikirim ke semua penerima yang dipilih...')
                    ->rows(4)
                    ->helperText('Pesan ini akan dikirim ke semua record yang dipilih'),
            ])
            ->action(function (\Illuminate\Database\Eloquent\Collection $records, array $data) use ($phoneField, $nameField) {
                self::sendBulkMessages($records, $data['bulk_message'], $phoneField, $nameField);
            })
            ->deselectRecordsAfterCompletion()
            ->requiresConfirmation()
            ->modalHeading('Konfirmasi Pengiriman WhatsApp Massal')
            ->modalDescription('Pastikan pesan sudah benar sebelum mengirim ke semua penerima yang dipilih.');
    }

    /**
     * Send bulk WhatsApp messages
     */
    protected static function sendBulkMessages(
        \Illuminate\Database\Eloquent\Collection $records,
        string $message,
        string $phoneField,
        string $nameField
    ): void {
        try {
            $whatsAppService = app(WhatsAppService::class);
            
            if (!$whatsAppService->isEnabled()) {
                Notification::make()
                    ->title('WhatsApp Tidak Aktif')
                    ->body('Layanan WhatsApp sedang tidak aktif')
                    ->warning()
                    ->send();
                return;
            }

            $recipients = [];
            $invalidRecords = [];

            foreach ($records as $record) {
                $phone = $record->{$phoneField} ?? null;
                $name = $record->{$nameField} ?? 'Pengguna';

                if (empty($phone)) {
                    $invalidRecords[] = $name;
                    continue;
                }

                $recipients[] = [
                    'phone' => $phone,
                    'message' => $message,
                    'name' => $name
                ];
            }

            if (!empty($invalidRecords)) {
                Notification::make()
                    ->title('Data Tidak Lengkap')
                    ->body('Beberapa data tidak memiliki nomor WhatsApp: ' . implode(', ', array_slice($invalidRecords, 0, 3)) . (count($invalidRecords) > 3 ? ' dan lainnya' : ''))
                    ->warning()
                    ->send();
            }

            if (empty($recipients)) {
                Notification::make()
                    ->title('Tidak Ada Penerima Valid')
                    ->body('Tidak ada data dengan nomor WhatsApp yang valid')
                    ->danger()
                    ->send();
                return;
            }

            $results = $whatsAppService->sendBulkMessages($recipients);
            
            $successCount = count(array_filter($results, fn($r) => $r['success']));
            $failCount = count($results) - $successCount;

            Notification::make()
                ->title('Pengiriman WhatsApp Selesai')
                ->body("Berhasil: {$successCount}, Gagal: {$failCount}")
                ->success()
                ->send();

        } catch (Exception $e) {
            Notification::make()
                ->title('Error Pengiriman Massal')
                ->body('Terjadi kesalahan: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
