<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Karyawan;
use App\Models\CutiIzin;
use App\Models\Schedule;
use App\Models\Absensi;
use App\Models\Shift;
use App\Models\Entitas;
use App\Models\Departemen;
use App\Models\Divisi;
use App\Models\Jabatan;
use App\Filament\Karyawan\Resources\StatusKaryawanResource;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class StatusKaryawanResourceTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $karyawan;
    protected $entitas;
    protected $departemen;
    protected $divisi;
    protected $jabatan;
    protected $shift;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestData();
    }

    protected function setupTestData()
    {
        // Create basic organizational structure
        $this->entitas = Entitas::factory()->create(['nama_entitas' => 'Test Store']);
        $this->departemen = Departemen::factory()->create(['nama_departemen' => 'Test Department']);
        $this->divisi = Divisi::factory()->create(['nama_divisi' => 'Test Division']);
        $this->jabatan = Jabatan::factory()->create(['nama_jabatan' => 'Test Position']);
        $this->shift = Shift::factory()->create([
            'nama_shift' => 'Regular',
            'waktu_mulai' => '08:00:00',
            'waktu_selesai' => '17:00:00'
        ]);

        // Create user and karyawan
        $this->user = User::factory()->create(['role' => 'karyawan']);
        $this->karyawan = Karyawan::factory()->create([
            'id_user' => $this->user->id,
            'id_entitas' => $this->entitas->id,
            'id_departemen' => $this->departemen->id,
            'id_divisi' => $this->divisi->id,
            'id_jabatan' => $this->jabatan->id,
            'status_aktif' => true,
        ]);
    }

    /** @test */
    public function it_can_determine_cuti_status()
    {
        $today = Carbon::today();
        
        // Create approved cuti
        CutiIzin::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => $today,
            'tanggal_selesai' => $today->copy()->addDays(2),
            'status' => 'approved'
        ]);

        $status = StatusKaryawanResource::getStatusHariIni($this->karyawan);
        
        $this->assertEquals('cuti', $status);
    }

    /** @test */
    public function it_can_determine_izin_status()
    {
        $today = Carbon::today();
        
        // Create approved izin
        CutiIzin::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'izin',
            'tanggal_mulai' => $today,
            'tanggal_selesai' => $today,
            'status' => 'approved'
        ]);

        $status = StatusKaryawanResource::getStatusHariIni($this->karyawan);
        
        $this->assertEquals('izin', $status);
    }

    /** @test */
    public function it_can_determine_sakit_status()
    {
        $today = Carbon::today();
        
        // Create approved sakit
        CutiIzin::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'sakit',
            'tanggal_mulai' => $today,
            'tanggal_selesai' => $today,
            'status' => 'approved'
        ]);

        $status = StatusKaryawanResource::getStatusHariIni($this->karyawan);
        
        $this->assertEquals('sakit', $status);
    }

    /** @test */
    public function it_can_determine_no_schedule_status()
    {
        // No schedule created for today
        $status = StatusKaryawanResource::getStatusHariIni($this->karyawan);
        
        $this->assertEquals('tidak_ada_jadwal', $status);
    }

    /** @test */
    public function it_can_determine_hadir_status()
    {
        $today = Carbon::today();
        
        // Create schedule
        $schedule = Schedule::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => $today,
        ]);

        // Create attendance with hadir status
        Absensi::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => $today,
            'status' => 'hadir'
        ]);

        $status = StatusKaryawanResource::getStatusHariIni($this->karyawan);
        
        $this->assertEquals('hadir', $status);
    }

    /** @test */
    public function it_can_determine_terlambat_status()
    {
        $today = Carbon::today();
        
        // Create schedule
        $schedule = Schedule::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => $today,
        ]);

        // Create attendance with terlambat status
        Absensi::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => $today,
            'status' => 'terlambat'
        ]);

        $status = StatusKaryawanResource::getStatusHariIni($this->karyawan);
        
        $this->assertEquals('terlambat', $status);
    }

    /** @test */
    public function it_can_get_keterangan_for_cuti()
    {
        $today = Carbon::today();
        $tomorrow = $today->copy()->addDay();
        
        // Create approved cuti
        CutiIzin::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => $today,
            'tanggal_selesai' => $tomorrow,
            'status' => 'approved',
            'alasan' => 'Liburan keluarga'
        ]);

        $keterangan = StatusKaryawanResource::getKeteranganStatus($this->karyawan);
        
        $this->assertStringContainsString('Liburan keluarga', $keterangan);
        $this->assertStringContainsString($today->format('d/m'), $keterangan);
        $this->assertStringContainsString($tomorrow->format('d/m'), $keterangan);
    }

    /** @test */
    public function it_can_get_detailed_status_info()
    {
        $today = Carbon::today();
        
        // Create schedule
        $schedule = Schedule::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => $today,
        ]);

        // Create attendance
        $absensi = Absensi::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'jadwal_id' => $schedule->id,
            'tanggal_absensi' => $today,
            'status' => 'hadir'
        ]);

        $statusInfo = StatusKaryawanResource::getDetailedStatusInfo($this->karyawan);
        
        $this->assertEquals('hadir', $statusInfo['status']);
        $this->assertNotNull($statusInfo['jadwal']);
        $this->assertNotNull($statusInfo['absensi']);
        $this->assertEquals($schedule->id, $statusInfo['jadwal']->id);
        $this->assertEquals($absensi->id, $statusInfo['absensi']->id);
    }

    /** @test */
    public function cuti_status_takes_priority_over_schedule()
    {
        $today = Carbon::today();
        
        // Create schedule
        Schedule::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => $today,
        ]);

        // Create approved cuti (should take priority)
        CutiIzin::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => $today,
            'tanggal_selesai' => $today,
            'status' => 'approved'
        ]);

        $status = StatusKaryawanResource::getStatusHariIni($this->karyawan);
        
        $this->assertEquals('cuti', $status);
    }

    /** @test */
    public function pending_cuti_does_not_affect_status()
    {
        $today = Carbon::today();
        
        // Create schedule
        Schedule::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'shift_id' => $this->shift->id,
            'tanggal_jadwal' => $today,
        ]);

        // Create pending cuti (should not affect status)
        CutiIzin::factory()->create([
            'karyawan_id' => $this->karyawan->id,
            'jenis_permohonan' => 'cuti',
            'tanggal_mulai' => $today,
            'tanggal_selesai' => $today,
            'status' => 'pending'
        ]);

        $status = StatusKaryawanResource::getStatusHariIni($this->karyawan);
        
        // Should return libur since there's schedule but no attendance
        $this->assertEquals('libur', $status);
    }
}
