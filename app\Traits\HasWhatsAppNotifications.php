<?php

namespace App\Traits;

use App\Services\WhatsAppService;
use Filament\Actions\Action;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Exception;

trait HasWhatsAppNotifications
{
    /**
     * Create WhatsApp notification action
     *
     * @param string $phoneField Field name that contains phone number
     * @param string $defaultMessage Default message template
     * @return Action
     */
    protected function whatsAppNotificationAction(string $phoneField = 'phone', string $defaultMessage = ''): Action
    {
        return Action::make('sendWhatsApp')
            ->label('Kirim WhatsApp')
            ->icon('heroicon-o-chat-bubble-left-right')
            ->color('success')
            ->form([
                TextInput::make('phone_number')
                    ->label('Nomor WhatsApp')
                    ->placeholder('08123456789 atau 6281234567890')
                    ->required()
                    ->default(function () use ($phoneField) {
                        $record = $this->getRecord();
                        if ($record && isset($record->{$phoneField})) {
                            return $record->{$phoneField};
                        }
                        return '';
                    }),
                
                Textarea::make('message')
                    ->label('Pesan')
                    ->placeholder('Tulis pesan WhatsApp...')
                    ->required()
                    ->rows(4)
                    ->default($defaultMessage),
                
                Toggle::make('send_immediately')
                    ->label('Kirim Langsung')
                    ->default(true)
                    ->helperText('Jika dinonaktifkan, pesan akan ditampilkan untuk konfirmasi'),
            ])
            ->action(function (array $data) {
                $this->sendWhatsAppMessage($data['phone_number'], $data['message'], $data['send_immediately']);
            })
            ->visible(function () {
                $whatsAppService = app(WhatsAppService::class);
                return $whatsAppService->isEnabled();
            });
    }

    /**
     * Create bulk WhatsApp notification action
     *
     * @param string $phoneField Field name that contains phone number
     * @param string $nameField Field name that contains recipient name
     * @param string $defaultMessage Default message template (use {name} for personalization)
     * @return Action
     */
    protected function bulkWhatsAppNotificationAction(
        string $phoneField = 'phone', 
        string $nameField = 'name', 
        string $defaultMessage = ''
    ): Action {
        return Action::make('sendBulkWhatsApp')
            ->label('Kirim WhatsApp Massal')
            ->icon('heroicon-o-chat-bubble-left-right')
            ->color('warning')
            ->form([
                Textarea::make('message')
                    ->label('Pesan')
                    ->placeholder('Tulis pesan WhatsApp... (gunakan {name} untuk nama penerima)')
                    ->required()
                    ->rows(4)
                    ->default($defaultMessage)
                    ->helperText('Gunakan {name} untuk menampilkan nama penerima secara otomatis'),
                
                Toggle::make('confirm_send')
                    ->label('Konfirmasi Pengiriman')
                    ->default(false)
                    ->helperText('Centang untuk mengkonfirmasi pengiriman pesan massal'),
            ])
            ->action(function (array $data) use ($phoneField, $nameField) {
                if (!$data['confirm_send']) {
                    Notification::make()
                        ->title('Konfirmasi Diperlukan')
                        ->body('Silakan centang konfirmasi pengiriman untuk melanjutkan')
                        ->warning()
                        ->send();
                    return;
                }

                $this->sendBulkWhatsAppMessages($data['message'], $phoneField, $nameField);
            })
            ->requiresConfirmation()
            ->modalHeading('Kirim WhatsApp Massal')
            ->modalDescription('Pastikan pesan sudah benar sebelum mengirim ke semua penerima yang dipilih.')
            ->visible(function () {
                $whatsAppService = app(WhatsAppService::class);
                return $whatsAppService->isEnabled();
            });
    }

    /**
     * Send WhatsApp message to single recipient
     *
     * @param string $phoneNumber
     * @param string $message
     * @param bool $sendImmediately
     * @return void
     */
    protected function sendWhatsAppMessage(string $phoneNumber, string $message, bool $sendImmediately = true): void
    {
        try {
            $whatsAppService = app(WhatsAppService::class);
            
            if (!$sendImmediately) {
                // Show confirmation with message preview
                Notification::make()
                    ->title('Pesan Siap Dikirim')
                    ->body("Ke: {$phoneNumber}\nPesan: " . substr($message, 0, 100) . (strlen($message) > 100 ? '...' : ''))
                    ->info()
                    ->send();
                return;
            }

            $result = $whatsAppService->sendMessage($phoneNumber, $message);

            if ($result['success']) {
                Notification::make()
                    ->title('WhatsApp Terkirim')
                    ->body("Pesan berhasil dikirim ke {$phoneNumber}")
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Gagal Mengirim WhatsApp')
                    ->body($result['error'] ?? 'Terjadi kesalahan saat mengirim pesan')
                    ->danger()
                    ->send();
            }
        } catch (Exception $e) {
            Notification::make()
                ->title('Error WhatsApp')
                ->body('Terjadi kesalahan: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Send bulk WhatsApp messages
     *
     * @param string $messageTemplate
     * @param string $phoneField
     * @param string $nameField
     * @return void
     */
    protected function sendBulkWhatsAppMessages(string $messageTemplate, string $phoneField, string $nameField): void
    {
        try {
            $whatsAppService = app(WhatsAppService::class);
            $selectedRecords = $this->getSelectedTableRecords();
            
            if ($selectedRecords->isEmpty()) {
                Notification::make()
                    ->title('Tidak Ada Data')
                    ->body('Silakan pilih data yang akan dikirim pesan WhatsApp')
                    ->warning()
                    ->send();
                return;
            }

            $recipients = [];
            $invalidRecords = [];

            foreach ($selectedRecords as $record) {
                $phone = $record->{$phoneField} ?? null;
                $name = $record->{$nameField} ?? 'Pengguna';

                if (empty($phone)) {
                    $invalidRecords[] = $name;
                    continue;
                }

                $personalizedMessage = str_replace('{name}', $name, $messageTemplate);
                
                $recipients[] = [
                    'phone' => $phone,
                    'message' => $personalizedMessage,
                    'name' => $name
                ];
            }

            if (!empty($invalidRecords)) {
                Notification::make()
                    ->title('Data Tidak Lengkap')
                    ->body('Beberapa data tidak memiliki nomor WhatsApp: ' . implode(', ', $invalidRecords))
                    ->warning()
                    ->send();
            }

            if (empty($recipients)) {
                Notification::make()
                    ->title('Tidak Ada Penerima Valid')
                    ->body('Tidak ada data dengan nomor WhatsApp yang valid')
                    ->danger()
                    ->send();
                return;
            }

            $results = $whatsAppService->sendBulkMessages($recipients);
            
            $successCount = count(array_filter($results, fn($r) => $r['success']));
            $failCount = count($results) - $successCount;

            $message = "Berhasil: {$successCount}, Gagal: {$failCount}";
            
            if ($failCount > 0) {
                $failedRecipients = array_filter($results, fn($r) => !$r['success']);
                $failedNames = array_map(fn($r) => $r['name'] ?? $r['phone'], $failedRecipients);
                $message .= "\nGagal: " . implode(', ', array_slice($failedNames, 0, 5));
                if (count($failedNames) > 5) {
                    $message .= ' dan ' . (count($failedNames) - 5) . ' lainnya';
                }
            }

            Notification::make()
                ->title('Pengiriman WhatsApp Selesai')
                ->body($message)
                ->success()
                ->send();

        } catch (Exception $e) {
            Notification::make()
                ->title('Error Pengiriman Massal')
                ->body('Terjadi kesalahan: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Get WhatsApp action for specific record
     *
     * @param Model $record
     * @param string $phoneField
     * @param string $messageTemplate
     * @return Action
     */
    protected function getRecordWhatsAppAction(Model $record, string $phoneField = 'phone', string $messageTemplate = ''): Action
    {
        return Action::make('sendWhatsAppToRecord')
            ->label('WhatsApp')
            ->icon('heroicon-o-chat-bubble-left-right')
            ->color('success')
            ->size('sm')
            ->action(function () use ($record, $phoneField, $messageTemplate) {
                $phone = $record->{$phoneField} ?? null;
                
                if (empty($phone)) {
                    Notification::make()
                        ->title('Nomor WhatsApp Tidak Tersedia')
                        ->body('Data ini tidak memiliki nomor WhatsApp')
                        ->warning()
                        ->send();
                    return;
                }

                $this->sendWhatsAppMessage($phone, $messageTemplate, true);
            })
            ->visible(function () use ($record, $phoneField) {
                $whatsAppService = app(WhatsAppService::class);
                return $whatsAppService->isEnabled() && !empty($record->{$phoneField});
            });
    }
}
