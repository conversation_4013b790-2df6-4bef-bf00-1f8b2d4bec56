# WhatsApp Notification untuk Cuti/Izin

Implementasi otomatis WhatsApp notification ketika ada pengajuan cuti/izin baru dan update status.

## 🎯 Fitur yang Diimplementasikan

### 1. **Notifikasi Pengajuan Baru**
- ✅ Otomatis kirim WhatsApp ke nomor hardcode `085272726519` ketika karyawan mengajukan cuti/izin
- ✅ Berisi detail lengkap: nama ka<PERSON>, NIP, jenis per<PERSON>, tanggal, alasan
- ✅ Format pesan yang informatif dengan emoji

### 2. **Notifikasi Update Status**
- ✅ Kirim notifikasi ke admin (nomor hardcode) ketika status berubah
- ✅ Kirim notifikasi ke karyawan (jika ada nomor HP) ketika status berubah
- ✅ Berbeda pesan untuk approval dan rejection

## 📱 Format Pesan

### **Pengajuan Baru (ke Admin)**
```
🔔 *PENGAJUAN CUTI/IZIN BARU*

Karyawan: *John <PERSON>*
NIP: EMP001
Jenis: *Cuti*
Tanggal: 15/07/2025 s/d 17/07/2025
Jumlah Hari: 3 hari
Alasan: Keperluan keluarga

Keterangan: Acara pernikahan saudara

Status: *PENDING* (Menunggu Persetujuan)

Silakan cek sistem untuk review dan approval.

📅 Diajukan pada: 15/07/2025 09:30
```

### **Update Status (ke Admin)**
```
📋 *UPDATE STATUS CUTI/IZIN*

Karyawan: *John Doe*
NIP: EMP001
Jenis: *Cuti*
Tanggal: 15/07/2025 s/d 17/07/2025
Status: *DISETUJUI ✅*

⏰ Diupdate pada: 15/07/2025 10:15
```

### **Update Status (ke Karyawan)**
```
Halo John Doe,

Permohonan Cuti Anda untuk tanggal 15/07/2025 s/d 17/07/2025 telah *DISETUJUI ✅*.

Silakan koordinasi dengan atasan langsung Anda.

Terima kasih.
Tim HRD
```

## 🔧 Konfigurasi

### 1. **Nomor Hardcode**
Nomor admin hardcode: `085272726519` (otomatis diformat ke `6285272726519`)

### 2. **Trigger Events**
- **Created**: Ketika cuti/izin baru dibuat (status: pending)
- **Updated**: Ketika status berubah ke approved/rejected

## 🧪 Testing

### **Test Manual**
```bash
# Test service WhatsApp
php artisan whatsapp:test 6285272726519 "Test message"

# Test notifikasi cuti
php artisan whatsapp:test-cuti

# Test dengan karyawan specific
php artisan whatsapp:test-cuti 1
```

### **Test Scenario**
1. Buat pengajuan cuti baru → Cek WhatsApp admin
2. Approve cuti → Cek WhatsApp admin & karyawan
3. Reject cuti → Cek WhatsApp admin & karyawan

## 📋 Log Monitoring

Semua aktivitas WhatsApp dicatat di log Laravel:

```bash
# Monitor log real-time
tail -f storage/logs/laravel.log | grep WhatsApp

# Cari log specific
grep "WhatsApp notification" storage/logs/laravel.log
```

### **Log Entries**
- ✅ `WhatsApp notification sent for new leave request`
- ✅ `WhatsApp notification sent for status update`
- ⚠️ `Failed to send WhatsApp notification`
- ❌ `WhatsApp notification error`

## 🔍 Troubleshooting

### **Notifikasi tidak terkirim**
1. Cek status service:
   ```bash
   php artisan tinker
   >>> app(\App\Services\WhatsAppService::class)->getStatus()
   ```

2. Cek log error:
   ```bash
   tail -f storage/logs/laravel.log | grep "WhatsApp.*error"
   ```

3. Cek konfigurasi:
   ```bash
   php artisan config:show services.starsender
   ```

### **Observer tidak jalan**
1. Pastikan Observer terdaftar di `AppServiceProvider`:
   ```php
   CutiIzin::observe(CutiIzinObserver::class);
   ```

2. Clear cache:
   ```bash
   php artisan config:clear
   php artisan cache:clear
   ```

## 🎛️ Customization

### **Ubah Nomor Admin**
Edit di `app/Observers/CutiIzinObserver.php`:
```php
// Line 135 & 198
$adminPhoneNumber = '6285272726519'; // Ganti dengan nomor baru
```

### **Ubah Format Pesan**
Edit method `sendNewLeaveRequestNotification()` dan `sendStatusUpdateNotification()` di Observer.

### **Tambah Kondisi**
Tambah kondisi di method `created()` atau `updated()`:
```php
public function created(CutiIzin $cutiIzin): void
{
    // Hanya kirim untuk jenis cuti tertentu
    if (in_array($cutiIzin->jenis_permohonan, ['cuti', 'sakit'])) {
        $this->sendNewLeaveRequestNotification($cutiIzin);
    }
}
```

## 📊 Monitoring & Analytics

### **Statistik Notifikasi**
```sql
-- Cek log notifikasi dari database log
SELECT * FROM logs 
WHERE message LIKE '%WhatsApp notification%' 
ORDER BY created_at DESC;
```

### **Performance Impact**
- Notifikasi berjalan asynchronous (tidak mengganggu proses utama)
- Error handling yang robust
- Timeout protection untuk API calls

## ✅ Checklist Implementasi

- [x] Observer untuk CutiIzin created/updated
- [x] WhatsApp service integration
- [x] Nomor hardcode admin (085272726519)
- [x] Format pesan yang informatif
- [x] Error handling & logging
- [x] Test command untuk debugging
- [x] Dokumentasi lengkap

## 🚀 Next Steps

1. **Test di environment production**
2. **Monitor log untuk error**
3. **Adjust format pesan sesuai kebutuhan**
4. **Tambah notifikasi untuk event lain jika diperlukan**

---

**Catatan**: Pastikan token Starsender sudah dikonfigurasi di `.env` dan service WhatsApp aktif sebelum testing.
